package cn.iflytek.imagesearch.domain.service;


import cn.iflytek.imagesearch.domain.model.request.ImageDetailRequest;
import cn.iflytek.imagesearch.domain.model.request.SearchRequest;
import cn.iflytek.imagesearch.domain.model.response.ImageDetailResponse;
import cn.iflytek.imagesearch.domain.model.response.SearchResult;

/**
 * 图片搜索服务接口
 */
public interface IImageSearchService {

    /**
     * 语义搜索图片
     * 基于自然语言描述进行智能图片搜索
     *
     * @param request 搜索请求
     * @return 搜索结果
     */
    SearchResult semanticSearch(SearchRequest request);


    /**
     * 获取图片详情
     * 获取单张图片的完整元数据信息
     *
     * @param request 详情请求
     * @return 图片详情
     */
    ImageDetailResponse getImageDetail(ImageDetailRequest request);


}
