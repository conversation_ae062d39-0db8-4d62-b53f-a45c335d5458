package cn.iflytek.imagesearch.domain.service;

import cn.iflytek.imagesearch.domain.model.entry.ImageEntity;
import cn.iflytek.imagesearch.domain.model.entry.ImageMetadataFilter;

import java.util.List;
import java.util.Set;

public interface MetadataSearchService {

    Set<String> filterByMetadata(ImageMetadataFilter filter);

    List<ImageEntity> buildSpecification(ImageMetadataFilter filter);




}
