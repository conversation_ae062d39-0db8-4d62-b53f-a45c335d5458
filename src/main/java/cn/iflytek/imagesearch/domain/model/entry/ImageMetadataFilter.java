package cn.iflytek.imagesearch.domain.model.entry;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class ImageMetadataFilter {
    private SizeRange sizeRange;             // 图片尺寸范围
    private Long minFileSize;                // 最小文件大小(字节)
    private Long maxFileSize;                // 最大文件大小(字节)
    private List<String> formats;            // 支持的格式 ["jpg", "png"]
    private DateRange dateRange;             // 时间范围
    private List<String> tags;               // 必须包含的标签
    private AspectRatioRange aspectRatio;    // 宽高比范围

    // 尺寸范围内部类
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class SizeRange {
        private Integer minWidth;
        private Integer maxWidth;
        private Integer minHeight;
        private Integer maxHeight;
    }

    // 日期范围内部类
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class DateRange {
        private LocalDateTime startDate;
        private LocalDateTime endDate;
    }

    // 宽高比范围内部类
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class AspectRatioRange {
        private Double minRatio;    // 最小宽高比
        private Double maxRatio;    // 最大宽高比
    }


    // 判断筛选条件是否为空
    public boolean isEmpty() {
        return sizeRange == null &&
                minFileSize == null &&
                maxFileSize == null &&
                (formats == null || formats.isEmpty()) &&
                dateRange == null &&
                (tags == null || tags.isEmpty()) &&
                aspectRatio == null;
    }

}
