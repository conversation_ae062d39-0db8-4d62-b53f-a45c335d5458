package cn.iflytek.imagesearch.domain.model.entry;

import lombok.Data;

/**
 * 图片信息
 */
@Data
public class ImageEntity {

    private String id;
    private String url;
    private ImageMetadata metadata;
    private float[] featureVector;        // 特征向量
    private float semanticScore;          // 语义相似度得分
    private float metadataScore;          // 元数据匹配得分
    private float combinedScore;          // 综合得分
}
