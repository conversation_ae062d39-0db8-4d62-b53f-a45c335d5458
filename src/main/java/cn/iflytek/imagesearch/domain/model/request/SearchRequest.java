package cn.iflytek.imagesearch.domain.model.request;

import cn.iflytek.imagesearch.domain.model.entry.ImageMetadataFilter;
import cn.iflytek.imagesearch.domain.model.entry.SearchStrategy;
import lombok.Data;

/**
 * 语义搜索图片请求
 */
@Data
public class SearchRequest {

    private String query;                    // 语义搜索查询
    private ImageMetadataFilter metadata;    // 元数据筛选条件
    private int limit = 50;                  // 返回结果数量
    private SearchStrategy strategy = SearchStrategy.HYBRID;  // 搜索策略


}
